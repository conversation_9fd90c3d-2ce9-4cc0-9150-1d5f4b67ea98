-- Services tablosuna sort_order kolonu ekleme
-- Tarih: 2025-01-29 15:30:00
-- Açıklama: <PERSON>zmet<PERSON> için sıralama sistemi ekleme

-- Services tablosuna sort_order kolonu ekle
ALTER TABLE services ADD COLUMN sort_order INTEGER DEFAULT 0;

-- Mevcut kayıtlar için sort_order değerlerini ata (created_at sırasına göre)
WITH ordered_services AS (
  SELECT id, ROW_NUMBER() OVER (PARTITION BY salon_id ORDER BY created_at) as rn
  FROM services
)
UPDATE services 
SET sort_order = ordered_services.rn
FROM ordered_services 
WHERE services.id = ordered_services.id;

-- Index ekle performans için
CREATE INDEX idx_services_salon_sort ON services(salon_id, sort_order);

-- Kontrol sorgusu
SELECT salon_id, name, sort_order, created_at 
FROM services 
ORDER BY salon_id, sort_order;
