/**
 * SMS Send API Route
 * Handles sending SMS notifications for appointments and other events
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import { smsNotificationsService, SMSNotificationType } from '@/lib/utils/sms-notifications';
import { smsRateLimitingService } from '@/lib/services/rate-limiting';

// Request validation schema
const sendSMSSchema = z.object({
  type: z.enum(['otp_verification', 'appointment_confirmation', 'appointment_cancelled', 'appointment_updated', 'appointment_reminder']),
  salonId: z.string().uuid('Geçersiz salon ID'),
  recipientPhone: z.string().min(10, 'Geçersiz telefon numarası'),
  appointmentId: z.string().uuid().optional(),
  otpVerificationId: z.string().uuid().optional(),
  templateData: z.record(z.any()),
  metadata: z.object({
    ipAddress: z.string().optional(),
    userAgent: z.string().optional(),
    source: z.string().optional()
  }).optional()
});

type SendSMSRequest = z.infer<typeof sendSMSSchema>;

/**
 * POST /api/sms/send
 * Send SMS notification
 */
export async function POST(request: NextRequest) {
  try {
      const supabase = createRouteHandlerClient({ cookies });

    // Get client IP and user agent
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Parse and validate request body
    let body: SendSMSRequest;
    try {
      const rawBody = await request.json();
      body = sendSMSSchema.parse(rawBody);
    } catch (error) {
      console.error('SMS send validation error:', error);
      return NextResponse.json({
        success: false,
        error: 'validation_error',
        message: 'Geçersiz istek verisi',
        details: error instanceof z.ZodError ? error.errors : undefined
      }, { status: 400 });
    }

    // Get current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    // For OTP verification, allow unauthenticated requests
    if (body.type !== 'otp_verification' && !session) {
      return NextResponse.json({
        success: false,
        error: 'unauthorized',
        message: 'Bu işlem için giriş yapmanız gerekiyor'
      }, { status: 401 });
    }

    // Verify salon access for authenticated requests
    if (session) {
      const hasAccess = await verifySalonAccess(supabase, session.user.id, body.salonId);
      if (!hasAccess) {
        return NextResponse.json({
          success: false,
          error: 'forbidden',
          message: 'Bu salona erişim yetkiniz yok'
        }, { status: 403 });
      }
    }

    // Check rate limits
    const rateLimitCheck = await smsRateLimitingService.checkRateLimit(
      body.salonId,
      body.recipientPhone,
      body.type
    );

    if (!rateLimitCheck.allowed) {
      return NextResponse.json({
        success: false,
        error: 'rate_limited',
        message: rateLimitCheck.reason || 'SMS gönderim limiti aşıldı',
        resetTime: rateLimitCheck.resetTime,
        remaining: rateLimitCheck.remaining
      }, { status: 429 });
    }

    // Prepare SMS notification data
    const smsData = {
      type: body.type as SMSNotificationType,
      salonId: body.salonId,
      recipientPhone: body.recipientPhone,
      appointmentId: body.appointmentId,
      otpVerificationId: body.otpVerificationId,
      templateData: body.templateData
    };

    // Send SMS notification
    const sendResult = await smsNotificationsService.sendSMSNotification(smsData);

    // Record the attempt for rate limiting
    await smsRateLimitingService.recordAttempt({
      salonId: body.salonId,
      phoneNumber: body.recipientPhone,
      messageType: body.type,
      timestamp: new Date(),
      success: sendResult.success,
      ipAddress: clientIP
    });

    // Return response
    if (sendResult.success) {
      return NextResponse.json({
        success: true,
        jobId: sendResult.jobId,
        deliveryLogId: sendResult.deliveryLogId,
        message: 'SMS başarıyla gönderildi'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: sendResult.error,
        message: sendResult.message || 'SMS gönderilemedi'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('SMS send API error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * GET /api/sms/send
 * Get SMS sending statistics and rate limit info
 */
export async function GET(request: NextRequest) {
  try {
      const supabase = createRouteHandlerClient({ cookies });

    // Get current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'unauthorized',
        message: 'Bu işlem için giriş yapmanız gerekiyor'
      }, { status: 401 });
    }

    // Get salon ID from query parameters
    const { searchParams } = new URL(request.url);
    const salonId = searchParams.get('salonId');

    if (!salonId) {
      return NextResponse.json({
        success: false,
        error: 'missing_salon_id',
        message: 'Salon ID gerekli'
      }, { status: 400 });
    }

    // Verify salon access
    const hasAccess = await verifySalonAccess(supabase, session.user.id, salonId);
    if (!hasAccess) {
      return NextResponse.json({
        success: false,
        error: 'forbidden',
        message: 'Bu salona erişim yetkiniz yok'
      }, { status: 403 });
    }

    // Get rate limit statistics
    const rateLimitStats = await smsRateLimitingService.getRateLimitStats(salonId);

    // Get recent SMS delivery logs
    const { data: recentSMS, error: smsError } = await supabase
      .from('sms_delivery_log')
      .select('*')
      .eq('salon_id', salonId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (smsError) {
      console.error('Error fetching SMS logs:', smsError);
    }

    return NextResponse.json({
      success: true,
      data: {
        rateLimits: rateLimitStats,
        recentSMS: recentSMS || [],
        statistics: {
          totalSent: rateLimitStats.current.daily,
          remainingDaily: rateLimitStats.limits.daily - rateLimitStats.current.daily,
          remainingHourly: rateLimitStats.limits.hourly - rateLimitStats.current.hourly
        }
      }
    });

  } catch (error) {
    console.error('SMS stats API error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * Verify if user has access to the salon
 */
async function verifySalonAccess(supabase: any, userId: string, salonId: string): Promise<boolean> {
  try {
    // Check if user is admin
    const { data: adminCheck } = await supabase.rpc('is_admin');
    if (adminCheck) {
      return true;
    }

    // Check if user is salon owner
    const { data: salon, error: salonError } = await supabase
      .from('salons')
      .select('owner_id')
      .eq('id', salonId)
      .single();

    if (salonError) {
      console.error('Error checking salon ownership:', salonError);
      return false;
    }

    if (salon?.owner_id === userId) {
      return true;
    }

    // Check if user is salon staff
    const { data: barber, error: barberError } = await supabase
      .from('barbers')
      .select('id')
      .eq('salon_id', salonId)
      .eq('user_id', userId)
      .single();

    if (barberError && barberError.code !== 'PGRST116') { // Not found error
      console.error('Error checking barber access:', barberError);
      return false;
    }

    return !!barber;

  } catch (error) {
    console.error('Error verifying salon access:', error);
    return false;
  }
}

/**
 * OPTIONS /api/sms/send
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
