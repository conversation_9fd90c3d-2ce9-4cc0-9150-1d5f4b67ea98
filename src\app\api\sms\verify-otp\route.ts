/**
 * OTP Verification API Route
 * Handles OTP generation and verification for SMS-based authentication
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import { otpService } from '@/lib/services/otp';
import { smsNotificationsService } from '@/lib/utils/sms-notifications';
import { smsRateLimitingService } from '@/lib/services/rate-limiting';

// OTP Generation Request Schema
const generateOTPSchema = z.object({
  salonId: z.string().uuid('Geçersiz salon ID'),
  phoneNumber: z.string().min(10, 'Geçersiz telefon numarası'),
  purpose: z.string().default('booking_verification'),
  contextData: z.record(z.any()).optional(),
  salonName: z.string().optional()
});

// OTP Verification Request Schema
const verifyOTPSchema = z.object({
  salonId: z.string().uuid('Geçersiz salon ID'),
  phoneNumber: z.string().min(10, 'Geçersiz telefon numarası'),
  otpCode: z.string().length(6, 'OTP kodu 6 haneli olmalıdır'),
  otpId: z.string().uuid().optional()
});

type GenerateOTPRequest = z.infer<typeof generateOTPSchema>;
type VerifyOTPRequest = z.infer<typeof verifyOTPSchema>;

/**
 * POST /api/sms/verify-otp
 * Generate or verify OTP
 */
export async function POST(request: NextRequest) {
  try {
      const supabase = createRouteHandlerClient({ cookies });

    // Get client IP and user agent for security
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Parse request body
    const rawBody = await request.json();
    const action = rawBody.action; // 'generate' or 'verify'

    if (action === 'generate') {
      return await handleGenerateOTP(rawBody, supabase, clientIP, userAgent);
    } else if (action === 'verify') {
      return await handleVerifyOTP(rawBody, supabase, clientIP, userAgent);
    } else {
      return NextResponse.json({
        success: false,
        error: 'invalid_action',
        message: 'Geçersiz işlem. "generate" veya "verify" olmalıdır.'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('OTP API error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * Handle OTP generation
 */
async function handleGenerateOTP(
  rawBody: any,
  supabase: any,
  clientIP: string,
  userAgent: string
): Promise<NextResponse> {
  try {
    // Validate request
    const body: GenerateOTPRequest = generateOTPSchema.parse(rawBody);

    // Sanitize phone number
    const sanitizedPhone = otpService.sanitizePhoneNumber(body.phoneNumber);
    
    // Validate phone number format
    if (!otpService.isValidTurkishPhone(sanitizedPhone)) {
      return NextResponse.json({
        success: false,
        error: 'invalid_phone',
        message: 'Geçersiz telefon numarası formatı'
      }, { status: 400 });
    }

    // Check rate limits for OTP generation
    const rateLimitCheck = await smsRateLimitingService.checkRateLimit(
      body.salonId,
      sanitizedPhone,
      'otp_verification'
    );

    if (!rateLimitCheck.allowed) {
      return NextResponse.json({
        success: false,
        error: 'rate_limited',
        message: rateLimitCheck.reason || 'OTP gönderim limiti aşıldı',
        resetTime: rateLimitCheck.resetTime
      }, { status: 429 });
    }

    // Generate OTP using RPC function for security
    const { data: otpResult, error: otpError } = await supabase.rpc('generate_otp_public', {
      p_salon_id: body.salonId,
      p_phone_number: sanitizedPhone,
      p_purpose: body.purpose,
      p_context_data: body.contextData || {},
      p_ip_address: clientIP,
      p_user_agent: userAgent
    });

    if (otpError || !otpResult?.success) {
      console.error('OTP generation error:', otpError || otpResult);
      return NextResponse.json({
        success: false,
        error: otpResult?.error || 'otp_generation_failed',
        message: otpResult?.message || 'OTP oluşturulamadı'
      }, { status: 500 });
    }

    // Send OTP via SMS
    const smsResult = await smsNotificationsService.sendOTPVerificationSMS(
      body.salonId,
      sanitizedPhone,
      otpResult.otp_code,
      body.salonName
    );

    // Record the SMS attempt
    await smsRateLimitingService.recordAttempt({
      salonId: body.salonId,
      phoneNumber: sanitizedPhone,
      messageType: 'otp_verification',
      timestamp: new Date(),
      success: smsResult.success,
      ipAddress: clientIP
    });

    if (!smsResult.success) {
      // OTP was generated but SMS failed - still return success but with warning
      return NextResponse.json({
        success: true,
        otpId: otpResult.otp_id,
        expiresAt: otpResult.expires_at,
        expiresInMinutes: otpResult.expires_in_minutes,
        warning: 'OTP oluşturuldu ancak SMS gönderilemedi',
        smsError: smsResult.message
      });
    }

    return NextResponse.json({
      success: true,
      otpId: otpResult.otp_id,
      expiresAt: otpResult.expires_at,
      expiresInMinutes: otpResult.expires_in_minutes,
      message: 'Doğrulama kodu gönderildi'
    });

  } catch (error) {
    console.error('Generate OTP error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'validation_error',
        message: 'Geçersiz istek verisi',
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'OTP oluşturulurken hata oluştu'
    }, { status: 500 });
  }
}

/**
 * Handle OTP verification
 */
async function handleVerifyOTP(
  rawBody: any,
  supabase: any,
  clientIP: string,
  userAgent: string
): Promise<NextResponse> {
  try {
    // Validate request
    const body: VerifyOTPRequest = verifyOTPSchema.parse(rawBody);

    // Sanitize phone number
    const sanitizedPhone = otpService.sanitizePhoneNumber(body.phoneNumber);
    
    // Validate phone number format
    if (!otpService.isValidTurkishPhone(sanitizedPhone)) {
      return NextResponse.json({
        success: false,
        error: 'invalid_phone',
        message: 'Geçersiz telefon numarası formatı'
      }, { status: 400 });
    }

    // Validate OTP code format
    if (!otpService.isValidOTPFormat(body.otpCode)) {
      return NextResponse.json({
        success: false,
        error: 'invalid_otp_format',
        message: 'Geçersiz doğrulama kodu formatı'
      }, { status: 400 });
    }

    // Verify OTP using RPC function for security
    const { data: verifyResult, error: verifyError } = await supabase.rpc('verify_otp_public', {
      p_salon_id: body.salonId,
      p_phone_number: sanitizedPhone,
      p_otp_code: body.otpCode,
      p_ip_address: clientIP,
      p_user_agent: userAgent
    });

    if (verifyError) {
      console.error('OTP verification error:', verifyError);
      return NextResponse.json({
        success: false,
        error: 'verification_failed',
        message: 'Doğrulama sırasında hata oluştu'
      }, { status: 500 });
    }

    if (!verifyResult?.success) {
      return NextResponse.json({
        success: false,
        error: verifyResult?.error || 'invalid_otp',
        message: verifyResult?.message || 'Geçersiz doğrulama kodu'
      }, { status: 400 });
    }

    // OTP verified successfully
    return NextResponse.json({
      success: true,
      otpId: verifyResult.otp_id,
      contextData: verifyResult.context_data,
      message: 'Doğrulama başarılı'
    });

  } catch (error) {
    console.error('Verify OTP error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'validation_error',
        message: 'Geçersiz istek verisi',
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Doğrulama sırasında hata oluştu'
    }, { status: 500 });
  }
}

/**
 * GET /api/sms/verify-otp
 * Get OTP verification status and statistics
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Get current user session (optional for stats)
    const { data: { session } } = await supabase.auth.getSession();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const salonId = searchParams.get('salonId');
    const phoneNumber = searchParams.get('phoneNumber');

    if (!salonId) {
      return NextResponse.json({
        success: false,
        error: 'missing_salon_id',
        message: 'Salon ID gerekli'
      }, { status: 400 });
    }

    // If authenticated, verify salon access
    if (session) {
      const hasAccess = await verifySalonAccess(supabase, session.user.id, salonId);
      if (!hasAccess) {
        return NextResponse.json({
          success: false,
          error: 'forbidden',
          message: 'Bu salona erişim yetkiniz yok'
        }, { status: 403 });
      }

      // Get OTP statistics for salon (authenticated users only)
      const { data: otpStats, error: statsError } = await supabase
        .from('otp_verifications')
        .select('purpose, is_verified, created_at, attempts')
        .eq('salon_id', salonId)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
        .order('created_at', { ascending: false })
        .limit(50);

      if (statsError) {
        console.error('Error fetching OTP stats:', statsError);
      }

      return NextResponse.json({
        success: true,
        data: {
          recentOTPs: otpStats || [],
          statistics: {
            total: otpStats?.length || 0,
            verified: otpStats?.filter(otp => otp.is_verified).length || 0,
            pending: otpStats?.filter(otp => !otp.is_verified).length || 0
          }
        }
      });
    }

    // For unauthenticated requests, just return basic info
    return NextResponse.json({
      success: true,
      data: {
        message: 'OTP servisi aktif',
        maxAttempts: 3,
        expiryMinutes: 5
      }
    });

  } catch (error) {
    console.error('OTP stats API error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * Verify if user has access to the salon
 */
async function verifySalonAccess(supabase: any, userId: string, salonId: string): Promise<boolean> {
  try {
    // Check if user is admin
    const { data: adminCheck } = await supabase.rpc('is_admin');
    if (adminCheck) {
      return true;
    }

    // Check if user is salon owner
    const { data: salon, error: salonError } = await supabase
      .from('salons')
      .select('owner_id')
      .eq('id', salonId)
      .single();

    if (salonError) {
      console.error('Error checking salon ownership:', salonError);
      return false;
    }

    if (salon?.owner_id === userId) {
      return true;
    }

    // Check if user is salon staff
    const { data: barber, error: barberError } = await supabase
      .from('barbers')
      .select('id')
      .eq('salon_id', salonId)
      .eq('user_id', userId)
      .single();

    if (barberError && barberError.code !== 'PGRST116') { // Not found error
      console.error('Error checking barber access:', barberError);
      return false;
    }

    return !!barber;

  } catch (error) {
    console.error('Error verifying salon access:', error);
    return false;
  }
}

/**
 * OPTIONS /api/sms/verify-otp
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
