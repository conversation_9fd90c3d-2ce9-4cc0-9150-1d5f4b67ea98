/**
 * Appointment Cancellation Token Utilities
 * Handles generation and management of secure cancellation tokens for SMS links
 */

import crypto from 'crypto';
import { supabaseClient } from '../supabase-singleton';

// Token configuration
const TOKEN_LENGTH = 32;
const TOKEN_EXPIRY_HOURS = 24;

// Cancellation token interface
export interface CancellationToken {
  id: string;
  token: string;
  appointmentId: string;
  salonId: string;
  expiresAt: Date;
  isUsed: boolean;
}

// Token generation result
export interface TokenGenerationResult {
  success: boolean;
  token?: string;
  tokenId?: string;
  expiresAt?: Date;
  cancelLink?: string;
  error?: string;
  message?: string;
}

/**
 * Cancellation Token Service
 */
export class CancellationTokenService {
  private supabase;

  constructor() {
    this.supabase = supabaseClient
  }

  /**
   * Generate a secure cancellation token for an appointment
   * @param appointmentId The appointment ID
   * @param salonId The salon ID
   * @returns Token generation result
   */
  async generateCancellationToken(
    appointmentId: string,
    salonId: string
  ): Promise<TokenGenerationResult> {
    try {
      // Generate secure random token
      const token = this.generateSecureToken();
      
      // Calculate expiry time
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + TOKEN_EXPIRY_HOURS);

      // Check if appointment exists and is not already cancelled
      const { data: appointment, error: appointmentError } = await this.supabase
        .from('appointments')
        .select('id, status, salon_id')
        .eq('id', appointmentId)
        .eq('salon_id', salonId)
        .single();

      if (appointmentError || !appointment) {
        return {
          success: false,
          error: 'appointment_not_found',
          message: 'Randevu bulunamadı'
        };
      }

      if (appointment.status === 'cancelled') {
        return {
          success: false,
          error: 'appointment_already_cancelled',
          message: 'Randevu zaten iptal edilmiş'
        };
      }

      // Invalidate any existing tokens for this appointment
      await this.invalidateExistingTokens(appointmentId);

      // Insert new token
      const { data: tokenData, error: tokenError } = await this.supabase
        .from('appointment_cancellation_tokens')
        .insert({
          appointment_id: appointmentId,
          salon_id: salonId,
          token,
          expires_at: expiresAt.toISOString(),
          is_used: false
        })
        .select('id')
        .single();

      if (tokenError || !tokenData) {
        console.error('Error creating cancellation token:', tokenError);
        return {
          success: false,
          error: 'token_creation_failed',
          message: 'İptal bağlantısı oluşturulamadı'
        };
      }

      // Generate cancellation link
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
      const cancelLink = `${baseUrl}/cancel/${token}`;

      return {
        success: true,
        token,
        tokenId: tokenData.id,
        expiresAt,
        cancelLink,
        message: 'İptal bağlantısı oluşturuldu'
      };

    } catch (error) {
      console.error('Error generating cancellation token:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'Sistem hatası oluştu'
      };
    }
  }

  /**
   * Validate a cancellation token
   * @param token The token to validate
   * @returns Validation result with appointment details
   */
  async validateCancellationToken(token: string) {
    try {
      const { data: tokenData, error: tokenError } = await this.supabase
        .from('appointment_cancellation_tokens')
        .select(`
          id,
          appointment_id,
          salon_id,
          is_used,
          expires_at,
          appointments (
            id,
            date,
            start_time,
            end_time,
            status,
            fullname,
            phonenumber,
            email,
            services (name, duration, price),
            barbers (name),
            salons (name, phone, address)
          )
        `)
        .eq('token', token)
        .single();

      if (tokenError || !tokenData) {
        return {
          success: false,
          error: 'token_not_found',
          message: 'Geçersiz iptal bağlantısı'
        };
      }

      // Check if token is already used
      if (tokenData.is_used) {
        return {
          success: false,
          error: 'token_already_used',
          message: 'Bu iptal bağlantısı daha önce kullanılmış'
        };
      }

      // Check if token has expired
      const expiresAt = new Date(tokenData.expires_at);
      if (expiresAt < new Date()) {
        return {
          success: false,
          error: 'token_expired',
          message: 'İptal bağlantısının süresi dolmuş'
        };
      }

      // Check if appointment exists and is not already cancelled
      const appointment = tokenData.appointments;
      if (!appointment) {
        return {
          success: false,
          error: 'appointment_not_found',
          message: 'Randevu bulunamadı'
        };
      }

      if (appointment.status === 'cancelled') {
        return {
          success: false,
          error: 'appointment_already_cancelled',
          message: 'Bu randevu zaten iptal edilmiş'
        };
      }

      return {
        success: true,
        data: {
          appointment: {
            id: appointment.id,
            date: appointment.date,
            time: appointment.start_time,
            endTime: appointment.end_time,
            customerName: appointment.fullname,
            customerPhone: appointment.phonenumber,
            customerEmail: appointment.email,
            serviceName: appointment.services?.name,
            serviceDuration: appointment.services?.duration,
            servicePrice: appointment.services?.price,
            barberName: appointment.barbers?.name,
            salonName: appointment.salons?.name,
            salonPhone: appointment.salons?.phone,
            salonAddress: appointment.salons?.address
          },
          token: {
            id: tokenData.id,
            expiresAt: tokenData.expires_at
          }
        }
      };

    } catch (error) {
      console.error('Error validating cancellation token:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'Sistem hatası oluştu'
      };
    }
  }

  /**
   * Use a cancellation token to cancel an appointment
   * @param token The cancellation token
   * @param ipAddress Client IP address for logging
   * @param userAgent Client user agent for logging
   * @returns Cancellation result
   */
  async useCancellationToken(
    token: string,
    ipAddress?: string,
    userAgent?: string
  ) {
    try {
      // First validate the token
      const validation = await this.validateCancellationToken(token);
      if (!validation.success) {
        return validation;
      }

      const appointmentId = validation.data.appointment.id;
      const salonId = validation.data.token.id; // This should be salon_id from token data

      // Mark token as used
      const { error: tokenUpdateError } = await this.supabase
        .from('appointment_cancellation_tokens')
        .update({
          is_used: true,
          used_at: new Date().toISOString(),
          used_ip_address: ipAddress,
          used_user_agent: userAgent
        })
        .eq('token', token);

      if (tokenUpdateError) {
        console.error('Error marking token as used:', tokenUpdateError);
        return {
          success: false,
          error: 'token_update_failed',
          message: 'İptal işlemi sırasında hata oluştu'
        };
      }

      // Cancel the appointment
      const { error: appointmentUpdateError } = await this.supabase
        .from('appointments')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', appointmentId);

      if (appointmentUpdateError) {
        console.error('Error cancelling appointment:', appointmentUpdateError);
        return {
          success: false,
          error: 'appointment_cancel_failed',
          message: 'Randevu iptal edilemedi'
        };
      }

      return {
        success: true,
        data: {
          appointmentId,
          customerName: validation.data.appointment.customerName,
          customerPhone: validation.data.appointment.customerPhone,
          appointmentDate: validation.data.appointment.date,
          appointmentTime: validation.data.appointment.time
        },
        message: 'Randevu başarıyla iptal edildi'
      };

    } catch (error) {
      console.error('Error using cancellation token:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'İptal işlemi sırasında sistem hatası oluştu'
      };
    }
  }

  /**
   * Generate a secure random token
   * @returns Secure random token string
   */
  private generateSecureToken(): string {
    return crypto.randomBytes(TOKEN_LENGTH).toString('hex');
  }

  /**
   * Invalidate existing tokens for an appointment
   * @param appointmentId The appointment ID
   */
  private async invalidateExistingTokens(appointmentId: string): Promise<void> {
    try {
      await this.supabase
        .from('appointment_cancellation_tokens')
        .update({ is_used: true })
        .eq('appointment_id', appointmentId)
        .eq('is_used', false);
    } catch (error) {
      console.error('Error invalidating existing tokens:', error);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Clean up expired tokens (should be run periodically)
   */
  async cleanupExpiredTokens(): Promise<{ deleted: number }> {
    try {
      const { data, error } = await this.supabase
        .from('appointment_cancellation_tokens')
        .delete()
        .lt('expires_at', new Date().toISOString())
        .select('id');

      if (error) {
        console.error('Error cleaning up expired tokens:', error);
        return { deleted: 0 };
      }

      return { deleted: data?.length || 0 };
    } catch (error) {
      console.error('Error in cleanupExpiredTokens:', error);
      return { deleted: 0 };
    }
  }
}

// Export singleton instance
export const cancellationTokenService = new CancellationTokenService();

// Export helper functions
export async function generateAppointmentCancellationToken(
  appointmentId: string,
  salonId: string
): Promise<TokenGenerationResult> {
  return cancellationTokenService.generateCancellationToken(appointmentId, salonId);
}

export async function validateAppointmentCancellationToken(token: string) {
  return cancellationTokenService.validateCancellationToken(token);
}

export async function useAppointmentCancellationToken(
  token: string,
  ipAddress?: string,
  userAgent?: string
) {
  return cancellationTokenService.useCancellationToken(token, ipAddress, userAgent);
}
