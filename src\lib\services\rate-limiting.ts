/**
 * Rate Limiting Service for SMS Notifications
 * Handles rate limiting for SMS sending to prevent abuse and manage costs
 */

import { supabaseClient } from '../supabase-singleton';

// Rate Limit Types
export interface RateLimitConfig {
  dailyLimit: number;
  hourlyLimit: number;
  perPhoneHourlyLimit: number;
  perPhoneDailyLimit: number;
}

export interface RateLimitStatus {
  allowed: boolean;
  reason?: string;
  resetTime?: Date;
  remaining?: {
    daily: number;
    hourly: number;
    perPhoneDaily: number;
    perPhoneHourly: number;
  };
}

export interface RateLimitAttempt {
  salonId: string;
  phoneNumber: string;
  messageType: string;
  timestamp: Date;
  success: boolean;
  ipAddress?: string;
}

/**
 * SMS Rate Limiting Service
 */
export class SMSRateLimitingService {
  private supabase;
  private defaultConfig: RateLimitConfig = {
    dailyLimit: 1000,
    hourlyLimit: 100,
    perPhoneHourlyLimit: 5,
    perPhoneDailyLimit: 10
  };

  constructor() {
    this.supabase = supabaseClient;
  }

  /**
   * Check if SMS sending is allowed for a salon
   * @param salonId Salon ID
   * @param phoneNumber Recipient phone number
   * @param messageType Type of SMS message
   * @returns Rate limit status
   */
  async checkRateLimit(
    salonId: string,
    phoneNumber: string,
    messageType: string
  ): Promise<RateLimitStatus> {
    try {
      // Get salon SMS settings and current usage
      const salonSettings = await this.getSalonRateLimits(salonId);
      const currentUsage = await this.getCurrentUsage(salonId, phoneNumber);

      // Check global system limits first
      const systemLimits = await this.checkSystemLimits();
      if (!systemLimits.allowed) {
        return systemLimits;
      }

      // Check salon-specific limits
      const salonLimits = this.checkSalonLimits(salonSettings, currentUsage);
      if (!salonLimits.allowed) {
        return salonLimits;
      }

      // Check per-phone limits
      const phoneLimits = this.checkPhoneLimits(currentUsage, salonSettings);
      if (!phoneLimits.allowed) {
        return phoneLimits;
      }

      // All checks passed
      return {
        allowed: true,
        remaining: {
          daily: Math.max(0, salonSettings.dailyLimit - currentUsage.dailyCount),
          hourly: Math.max(0, salonSettings.hourlyLimit - currentUsage.hourlyCount),
          perPhoneDaily: Math.max(0, salonSettings.perPhoneDailyLimit - currentUsage.phoneDaily),
          perPhoneHourly: Math.max(0, salonSettings.perPhoneHourlyLimit - currentUsage.phoneHourly)
        }
      };

    } catch (error) {
      console.error('Rate limit check error:', error);
      // On error, allow the request but log it
      return { allowed: true };
    }
  }

  /**
   * Record an SMS sending attempt
   * @param attempt SMS attempt details
   */
  async recordAttempt(attempt: RateLimitAttempt): Promise<void> {
    try {
      // Log the attempt
      await this.supabase
        .from('sms_delivery_log')
        .insert({
          salon_id: attempt.salonId,
          recipient_phone: attempt.phoneNumber,
          message_type: attempt.messageType,
          delivery_status: attempt.success ? 'sent' : 'failed',
          created_at: attempt.timestamp.toISOString(),
          ip_address: attempt.ipAddress
        });

      // Update salon counters if successful
      if (attempt.success) {
        await this.updateSalonCounters(attempt.salonId);
      }

    } catch (error) {
      console.error('Error recording SMS attempt:', error);
    }
  }

  /**
   * Get salon rate limit configuration
   */
  private async getSalonRateLimits(salonId: string): Promise<RateLimitConfig> {
    try {
      const { data } = await this.supabase
        .from('salon_sms_settings')
        .select('daily_sms_limit, hourly_sms_limit')
        .eq('salon_id', salonId)
        .single();

      if (data) {
        return {
          dailyLimit: data.daily_sms_limit || this.defaultConfig.dailyLimit,
          hourlyLimit: data.hourly_sms_limit || this.defaultConfig.hourlyLimit,
          perPhoneHourlyLimit: this.defaultConfig.perPhoneHourlyLimit,
          perPhoneDailyLimit: this.defaultConfig.perPhoneDailyLimit
        };
      }

      return this.defaultConfig;
    } catch (error) {
      console.error('Error getting salon rate limits:', error);
      return this.defaultConfig;
    }
  }

  /**
   * Get current usage statistics
   */
  private async getCurrentUsage(salonId: string, phoneNumber: string) {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Get salon daily count
      const { count: dailyCount } = await this.supabase
        .from('sms_delivery_log')
        .select('*', { count: 'exact', head: true })
        .eq('salon_id', salonId)
        .eq('delivery_status', 'sent')
        .gte('created_at', oneDayAgo.toISOString());

      // Get salon hourly count
      const { count: hourlyCount } = await this.supabase
        .from('sms_delivery_log')
        .select('*', { count: 'exact', head: true })
        .eq('salon_id', salonId)
        .eq('delivery_status', 'sent')
        .gte('created_at', oneHourAgo.toISOString());

      // Get per-phone daily count
      const { count: phoneDaily } = await this.supabase
        .from('sms_delivery_log')
        .select('*', { count: 'exact', head: true })
        .eq('salon_id', salonId)
        .eq('recipient_phone', phoneNumber)
        .eq('delivery_status', 'sent')
        .gte('created_at', oneDayAgo.toISOString());

      // Get per-phone hourly count
      const { count: phoneHourly } = await this.supabase
        .from('sms_delivery_log')
        .select('*', { count: 'exact', head: true })
        .eq('salon_id', salonId)
        .eq('recipient_phone', phoneNumber)
        .eq('delivery_status', 'sent')
        .gte('created_at', oneHourAgo.toISOString());

      return {
        dailyCount: dailyCount || 0,
        hourlyCount: hourlyCount || 0,
        phoneDaily: phoneDaily || 0,
        phoneHourly: phoneHourly || 0
      };

    } catch (error) {
      console.error('Error getting current usage:', error);
      return {
        dailyCount: 0,
        hourlyCount: 0,
        phoneDaily: 0,
        phoneHourly: 0
      };
    }
  }

  /**
   * Check system-wide limits
   */
  private async checkSystemLimits(): Promise<RateLimitStatus> {
    try {
      const { data: systemConfig } = await this.supabase
        .from('system_sms_config')
        .select('is_sms_enabled, emergency_disable, global_daily_limit, global_hourly_limit')
        .single();

      if (!systemConfig) {
        return { allowed: true };
      }

      // Check if SMS is globally disabled
      if (!systemConfig.is_sms_enabled || systemConfig.emergency_disable) {
        return {
          allowed: false,
          reason: 'SMS servisi sistem yöneticisi tarafından devre dışı bırakılmış'
        };
      }

      // Check global limits (if configured)
      if (systemConfig.global_daily_limit || systemConfig.global_hourly_limit) {
        const globalUsage = await this.getGlobalUsage();

        if (systemConfig.global_daily_limit && globalUsage.daily >= systemConfig.global_daily_limit) {
          return {
            allowed: false,
            reason: 'Günlük sistem limiti aşıldı',
            resetTime: this.getNextDayReset()
          };
        }

        if (systemConfig.global_hourly_limit && globalUsage.hourly >= systemConfig.global_hourly_limit) {
          return {
            allowed: false,
            reason: 'Saatlik sistem limiti aşıldı',
            resetTime: this.getNextHourReset()
          };
        }
      }

      return { allowed: true };

    } catch (error) {
      console.error('Error checking system limits:', error);
      return { allowed: true }; // Allow on error
    }
  }

  /**
   * Check salon-specific limits
   */
  private checkSalonLimits(
    config: RateLimitConfig,
    usage: { dailyCount: number; hourlyCount: number }
  ): RateLimitStatus {
    // Check daily limit
    if (usage.dailyCount >= config.dailyLimit) {
      return {
        allowed: false,
        reason: 'Günlük SMS limiti aşıldı',
        resetTime: this.getNextDayReset()
      };
    }

    // Check hourly limit
    if (usage.hourlyCount >= config.hourlyLimit) {
      return {
        allowed: false,
        reason: 'Saatlik SMS limiti aşıldı',
        resetTime: this.getNextHourReset()
      };
    }

    return { allowed: true };
  }

  /**
   * Check per-phone limits
   */
  private checkPhoneLimits(
    usage: { phoneDaily: number; phoneHourly: number },
    config: RateLimitConfig
  ): RateLimitStatus {
    // Check per-phone daily limit
    if (usage.phoneDaily >= config.perPhoneDailyLimit) {
      return {
        allowed: false,
        reason: 'Bu telefon numarası için günlük SMS limiti aşıldı',
        resetTime: this.getNextDayReset()
      };
    }

    // Check per-phone hourly limit
    if (usage.phoneHourly >= config.perPhoneHourlyLimit) {
      return {
        allowed: false,
        reason: 'Bu telefon numarası için saatlik SMS limiti aşıldı',
        resetTime: this.getNextHourReset()
      };
    }

    return { allowed: true };
  }

  /**
   * Get global system usage
   */
  private async getGlobalUsage() {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      const { count: daily } = await this.supabase
        .from('sms_delivery_log')
        .select('*', { count: 'exact', head: true })
        .eq('delivery_status', 'sent')
        .gte('created_at', oneDayAgo.toISOString());

      const { count: hourly } = await this.supabase
        .from('sms_delivery_log')
        .select('*', { count: 'exact', head: true })
        .eq('delivery_status', 'sent')
        .gte('created_at', oneHourAgo.toISOString());

      return {
        daily: daily || 0,
        hourly: hourly || 0
      };

    } catch (error) {
      console.error('Error getting global usage:', error);
      return { daily: 0, hourly: 0 };
    }
  }

  /**
   * Update salon SMS counters
   */
  private async updateSalonCounters(salonId: string): Promise<void> {
    try {
      const now = new Date();

      // Get current settings
      const { data: settings } = await this.supabase
        .from('salon_sms_settings')
        .select('daily_sms_sent, hourly_sms_sent, last_daily_reset, last_hourly_reset')
        .eq('salon_id', salonId)
        .single();

      if (!settings) {
        // Create initial settings if they don't exist
        await this.supabase
          .from('salon_sms_settings')
          .insert({
            salon_id: salonId,
            daily_sms_sent: 1,
            hourly_sms_sent: 1,
            last_daily_reset: now.toISOString(),
            last_hourly_reset: now.toISOString()
          });
        return;
      }

      // Check if we need to reset counters
      const lastDailyReset = new Date(settings.last_daily_reset);
      const lastHourlyReset = new Date(settings.last_hourly_reset);

      const shouldResetDaily = this.shouldResetDailyCounter(lastDailyReset, now);
      const shouldResetHourly = this.shouldResetHourlyCounter(lastHourlyReset, now);

      let dailyCount = settings.daily_sms_sent;
      let hourlyCount = settings.hourly_sms_sent;

      if (shouldResetDaily) dailyCount = 0;
      if (shouldResetHourly) hourlyCount = 0;

      // Update counters
      await this.supabase
        .from('salon_sms_settings')
        .update({
          daily_sms_sent: dailyCount + 1,
          hourly_sms_sent: hourlyCount + 1,
          ...(shouldResetDaily && { last_daily_reset: now.toISOString() }),
          ...(shouldResetHourly && { last_hourly_reset: now.toISOString() })
        })
        .eq('salon_id', salonId);

    } catch (error) {
      console.error('Error updating salon counters:', error);
    }
  }

  /**
   * Check if daily counter should be reset
   */
  private shouldResetDailyCounter(lastReset: Date, now: Date): boolean {
    return now.getDate() !== lastReset.getDate() ||
           now.getMonth() !== lastReset.getMonth() ||
           now.getFullYear() !== lastReset.getFullYear();
  }

  /**
   * Check if hourly counter should be reset
   */
  private shouldResetHourlyCounter(lastReset: Date, now: Date): boolean {
    return now.getTime() - lastReset.getTime() >= 60 * 60 * 1000; // 1 hour
  }

  /**
   * Get next day reset time
   */
  private getNextDayReset(): Date {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }

  /**
   * Get next hour reset time
   */
  private getNextHourReset(): Date {
    const nextHour = new Date();
    nextHour.setHours(nextHour.getHours() + 1, 0, 0, 0);
    return nextHour;
  }

  /**
   * Get rate limit statistics for a salon
   */
  async getRateLimitStats(salonId: string): Promise<{
    current: { daily: number; hourly: number };
    limits: { daily: number; hourly: number };
    resetTimes: { daily: Date; hourly: Date };
  }> {
    try {
      const config = await this.getSalonRateLimits(salonId);
      const usage = await this.getCurrentUsage(salonId, '');

      return {
        current: {
          daily: usage.dailyCount,
          hourly: usage.hourlyCount
        },
        limits: {
          daily: config.dailyLimit,
          hourly: config.hourlyLimit
        },
        resetTimes: {
          daily: this.getNextDayReset(),
          hourly: this.getNextHourReset()
        }
      };

    } catch (error) {
      console.error('Error getting rate limit stats:', error);
      return {
        current: { daily: 0, hourly: 0 },
        limits: { daily: this.defaultConfig.dailyLimit, hourly: this.defaultConfig.hourlyLimit },
        resetTimes: { daily: this.getNextDayReset(), hourly: this.getNextHourReset() }
      };
    }
  }
}

// Export singleton instance
export const smsRateLimitingService = new SMSRateLimitingService();
