/**
 * SMS Settings API Route
 * Handles SMS configuration management for salons
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import { encryptNetGSMCredentials } from '@/lib/services/netgsm';

// SMS Settings Schema
const smsSettingsSchema = z.object({
  is_enabled: z.boolean().default(false),
  use_system_default: z.boolean().default(true),
  netgsm_username: z.string().optional(),
  netgsm_password: z.string().optional(),
  netgsm_header: z.string().min(1).max(11).optional(),
  daily_sms_limit: z.coerce.number().min(1).max(10000).default(1000),
  hourly_sms_limit: z.coerce.number().min(1).max(1000).default(100),
  otp_template: z.string().min(10).optional(),
  appointment_confirmation_template: z.string().min(10).optional(),
  appointment_cancelled_template: z.string().min(10).optional(),
  appointment_updated_template: z.string().min(10).optional()
});

type SMSSettingsRequest = z.infer<typeof smsSettingsSchema>;

/**
 * GET /api/sms/settings
 * Get SMS settings for a salon
 */
export async function GET(request: NextRequest) {
  try {
      const supabase = createRouteHandlerClient({ cookies });

    // Get current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'unauthorized',
        message: 'Bu işlem için giriş yapmanız gerekiyor'
      }, { status: 401 });
    }

    // Get salon ID from query parameters
    const { searchParams } = new URL(request.url);
    const salonId = searchParams.get('salon_id');

    if (!salonId) {
      return NextResponse.json({
        success: false,
        error: 'missing_salon_id',
        message: 'Salon ID gerekli'
      }, { status: 400 });
    }

    // Verify salon access
    const hasAccess = await verifySalonAccess(supabase, session.user.id, salonId);
    if (!hasAccess) {
      return NextResponse.json({
        success: false,
        error: 'forbidden',
        message: 'Bu salona erişim yetkiniz yok'
      }, { status: 403 });
    }

    // Get SMS settings
    const { data: settings, error: settingsError } = await supabase
      .from('salon_sms_settings')
      .select(`
        id,
        salon_id,
        is_enabled,
        use_system_default,
        netgsm_header,
        daily_sms_limit,
        hourly_sms_limit,
        daily_sms_sent,
        hourly_sms_sent,
        current_balance,
        last_balance_check,
        otp_template,
        appointment_confirmation_template,
        appointment_cancelled_template,
        appointment_updated_template
      `)
      .eq('salon_id', salonId)
      .single();

    if (settingsError && settingsError.code !== 'PGRST116') { // Not found error
      console.error('Error fetching SMS settings:', settingsError);
      return NextResponse.json({
        success: false,
        error: 'database_error',
        message: 'SMS ayarları alınırken hata oluştu'
      }, { status: 500 });
    }

    // If no settings exist, return default values
    if (!settings) {
      return NextResponse.json({
        success: true,
        settings: {
          salon_id: salonId,
          is_enabled: false,
          use_system_default: true,
          netgsm_header: 'SALONFLOW',
          daily_sms_limit: 1000,
          hourly_sms_limit: 100,
          daily_sms_sent: 0,
          hourly_sms_sent: 0,
          current_balance: null,
          last_balance_check: null,
          otp_template: 'SalonFlow doğrulama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir.',
          appointment_confirmation_template: 'Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name}. İptal: {cancel_link}',
          appointment_cancelled_template: 'Randevunuz iptal edildi. {salon_name} - {date} {time} - {service_name}',
          appointment_updated_template: 'Randevunuz güncellendi. {salon_name} - Yeni tarih: {date} {time} - {service_name}'
        }
      });
    }

    return NextResponse.json({
      success: true,
      settings
    });

  } catch (error) {
    console.error('SMS settings GET error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * POST /api/sms/settings
 * Save SMS settings for a salon
 */
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'unauthorized',
        message: 'Bu işlem için giriş yapmanız gerekiyor'
      }, { status: 401 });
    }

    // Get salon ID from query parameters
    const { searchParams } = new URL(request.url);
    const salonId = searchParams.get('salon_id');

    if (!salonId) {
      return NextResponse.json({
        success: false,
        error: 'missing_salon_id',
        message: 'Salon ID gerekli'
      }, { status: 400 });
    }

    // Verify salon access (only owners can modify settings)
    const hasAccess = await verifySalonOwnership(supabase, session.user.id, salonId);
    if (!hasAccess) {
      return NextResponse.json({
        success: false,
        error: 'forbidden',
        message: 'Bu işlem için salon sahibi olmanız gerekiyor'
      }, { status: 403 });
    }

    // Parse and validate request body
    let body: SMSSettingsRequest;
    try {
      const rawBody = await request.json();
      body = smsSettingsSchema.parse(rawBody);
    } catch (error) {
      console.error('SMS settings validation error:', error);
      return NextResponse.json({
        success: false,
        error: 'validation_error',
        message: 'Geçersiz istek verisi',
        details: error instanceof z.ZodError ? error.errors : undefined
      }, { status: 400 });
    }

    // Prepare settings data
    const settingsData: any = {
      salon_id: salonId,
      is_enabled: body.is_enabled,
      use_system_default: body.use_system_default,
      netgsm_header: body.netgsm_header || 'SALONFLOW',
      daily_sms_limit: body.daily_sms_limit,
      hourly_sms_limit: body.hourly_sms_limit,
      otp_template: body.otp_template || 'SalonFlow doğrulama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir.',
      appointment_confirmation_template: body.appointment_confirmation_template || 'Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name}. İptal: {cancel_link}',
      appointment_cancelled_template: body.appointment_cancelled_template || 'Randevunuz iptal edildi. {salon_name} - {date} {time} - {service_name}',
      appointment_updated_template: body.appointment_updated_template || 'Randevunuz güncellendi. {salon_name} - Yeni tarih: {date} {time} - {service_name}',
      updated_by: session.user.id
    };

    // If using custom NetGSM credentials, encrypt them
    if (!body.use_system_default && body.netgsm_username && body.netgsm_password) {
      const encryptedCredentials = encryptNetGSMCredentials(body.netgsm_username, body.netgsm_password);
      settingsData.netgsm_username_encrypted = encryptedCredentials.encryptedUsername;
      settingsData.netgsm_password_encrypted = encryptedCredentials.encryptedPassword;
    } else {
      // Clear encrypted credentials if switching to system default
      settingsData.netgsm_username_encrypted = null;
      settingsData.netgsm_password_encrypted = null;
    }

    // Upsert SMS settings
    const { data: settings, error: settingsError } = await supabase
      .from('salon_sms_settings')
      .upsert(settingsData, { 
        onConflict: 'salon_id',
        ignoreDuplicates: false 
      })
      .select(`
        id,
        salon_id,
        is_enabled,
        use_system_default,
        netgsm_header,
        daily_sms_limit,
        hourly_sms_limit,
        daily_sms_sent,
        hourly_sms_sent,
        current_balance,
        last_balance_check,
        otp_template,
        appointment_confirmation_template,
        appointment_cancelled_template,
        appointment_updated_template
      `)
      .single();

    if (settingsError) {
      console.error('Error saving SMS settings:', settingsError);
      return NextResponse.json({
        success: false,
        error: 'database_error',
        message: 'SMS ayarları kaydedilirken hata oluştu'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      settings,
      message: 'SMS ayarları başarıyla kaydedildi'
    });

  } catch (error) {
    console.error('SMS settings POST error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * Verify if user has access to the salon
 */
async function verifySalonAccess(supabase: any, userId: string, salonId: string): Promise<boolean> {
  try {
    // Check if user is admin
    const { data: adminCheck } = await supabase.rpc('is_admin');
    if (adminCheck) {
      return true;
    }

    // Check if user is salon owner
    const { data: salon, error: salonError } = await supabase
      .from('salons')
      .select('owner_id')
      .eq('id', salonId)
      .single();

    if (salonError) {
      console.error('Error checking salon ownership:', salonError);
      return false;
    }

    if (salon?.owner_id === userId) {
      return true;
    }

    // Check if user is salon staff
    const { data: barber, error: barberError } = await supabase
      .from('barbers')
      .select('id')
      .eq('salon_id', salonId)
      .eq('user_id', userId)
      .single();

    if (barberError && barberError.code !== 'PGRST116') { // Not found error
      console.error('Error checking barber access:', barberError);
      return false;
    }

    return !!barber;

  } catch (error) {
    console.error('Error verifying salon access:', error);
    return false;
  }
}

/**
 * Verify if user is the salon owner (for settings modification)
 */
async function verifySalonOwnership(supabase: any, userId: string, salonId: string): Promise<boolean> {
  try {
    // Check if user is admin
    const { data: adminCheck } = await supabase.rpc('is_admin');
    if (adminCheck) {
      return true;
    }

    // Check if user is salon owner
    const { data: salon, error: salonError } = await supabase
      .from('salons')
      .select('owner_id')
      .eq('id', salonId)
      .single();

    if (salonError) {
      console.error('Error checking salon ownership:', salonError);
      return false;
    }

    return salon?.owner_id === userId;

  } catch (error) {
    console.error('Error verifying salon ownership:', error);
    return false;
  }
}

/**
 * OPTIONS /api/sms/settings
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
